<script setup lang="ts">
import { createBrowserInspector } from '@statelyai/inspect'

// Call inspect to enable real-time debugging
const { inspect } = createBrowserInspector({})

import { createMachine, assign } from 'xstate'
import { useMachine } from '@xstate/vue'

// Mock service to simulate fetching initial data
const fetchInitialData = () => {
  return new Promise((resolve) => {
    // Simulate API delay
    setTimeout(() => {
      // Generate random user data to simulate different purchase flows
      const isAutoBuy = Math.random() > 0.5
      const isVerified = Math.random() > 0.7
      const subscriptionStatus = ['None', 'Basic', 'Premium'][Math.floor(Math.random() * 3)]
      const subscriptionTier = Math.floor(Math.random() * 3)
      const kycLevel = ['Level 1', 'Level 2', 'Level 3'][Math.floor(Math.random() * 3)]
      const balance = Math.floor(Math.random() * 1000)
      const cardType = ['Virtual', 'Physical'][Math.floor(Math.random() * 2)]
      const productCategory = ['Electronics', 'Clothing', 'Books'][Math.floor(Math.random() * 3)]
      const hasPromoCode = Math.random() > 0.7

      console.log('Initial data generated:', {
        isAutoBuy,
        isVerified,
        subscriptionStatus,
        kycLevel,
        balance,
      })

      resolve({
        isAutoBuy,
        isVerified,
        subscriptionStatus,
        subscriptionTier,
        kycLevel,
        balance,
        cardType,
        productCategory,
        availablePaymentMethods: ['Card', 'Bank Transfer', 'Crypto'],
        hasPromoCode,
      })
    }, 1000)
  })
}

// Define the purchase flow state machine
const purchaseFlowMachine = createMachine({
  id: 'purchaseFlow',
  initial: 'initialization',
  context: {
    // Store any data related to the purchase flow
    product: {
      id: 'prod-123',
      name: 'Premium Headphones',
      price: 99.99,
      description: 'High-quality wireless headphones with noise cancellation',
      availableColors: ['Black', 'White', 'Blue'],
      category: 'Electronics',
    },
    userInfo: {
      subscription: 'Premium', // 'None', 'Basic', 'Premium'
      subscriptionTier: 2, // 0: None, 1: Basic, 2: Premium
      isVerified: false, // Whether the user is verified
      kycLevel: 'Level 2',
      balance: 500.0,
      selectedColor: null,
      isAutoBuy: false,
      selectedPaymentMethod: null,
      promoCode: '',
      hasPromoCode: false,
    },
    flowInfo: {
      numSteps: 0,
      currentStep: 0,
      paymentMethods: ['Card', 'Bank Transfer', 'Crypto'],
      loading: false,
      error: null,
    },
  },
  states: {
    initialization: {
      tags: ['loading'],
      invoke: {
        id: 'fetchInitialData',
        src: fetchInitialData,
        onDone: [
          {
            // If user is not verified, go to product selection but mark for verification later
            target: 'productSelection',
            actions: assign({
              userInfo: ({ context }, event) => ({
                ...context.userInfo,
                isAutoBuy: event.data.isAutoBuy,
                isVerified: event.data.isVerified,
                subscription: event.data.subscriptionStatus,
                subscriptionTier: event.data.subscriptionTier,
                kycLevel: event.data.kycLevel,
                balance: event.data.balance,
                hasPromoCode: event.data.hasPromoCode,
              }),
              flowInfo: (context) => ({
                ...context.flowInfo,
                paymentMethods: event.data.availablePaymentMethods,
              }),
              product: (context) => ({
                ...context.product,
                category: event.data.productCategory,
                cardType: event.data.cardType,
              }),
            }),
            guard: ({ event }) => !event.data.isVerified,
          },
          {
            // If user has low balance, go to product selection but mark for balance warning
            target: 'productSelection',
            actions: assign({
              userInfo: (context, event) => ({
                ...context.userInfo,
                isAutoBuy: event.data.isAutoBuy,
                isVerified: event.data.isVerified,
                subscription: event.data.subscriptionStatus,
                subscriptionTier: event.data.subscriptionTier,
                kycLevel: event.data.kycLevel,
                balance: event.data.balance,
                hasPromoCode: event.data.hasPromoCode,
              }),
              flowInfo: (context) => ({
                ...context.flowInfo,
                paymentMethods: event.data.availablePaymentMethods,
              }),
              product: (context) => ({
                ...context.product,
                category: event.data.productCategory,
                cardType: event.data.cardType,
              }),
            }),
            guard: ({ event }) => event.data.balance < 100,
          },
          {
            // Default path for other cases
            target: 'productSelection',
            actions: assign({
              userInfo: (context, event) => ({
                ...context.userInfo,
                isAutoBuy: event.data.isAutoBuy,
                isVerified: event.data.isVerified,
                subscription: event.data.subscriptionStatus,
                subscriptionTier: event.data.subscriptionTier,
                kycLevel: event.data.kycLevel,
                balance: event.data.balance,
                hasPromoCode: event.data.hasPromoCode,
              }),
              flowInfo: (context) => ({
                ...context.flowInfo,
                paymentMethods: event.data.availablePaymentMethods,
              }),
              product: (context) => ({
                ...context.product,
                category: event.data.productCategory,
                cardType: event.data.cardType,
              }),
            }),
          },
        ],
        onError: {
          target: 'error',
          actions: assign({
            flowInfo: (context, event) => ({
              ...context.flowInfo,
              error: event.data,
            }),
          }),
        },
      },
    },
    error: {
      on: {
        RETRY: 'initialization',
      },
    },
    productSelection: {
      entry: assign({
        flowInfo: (context) => ({
          ...context.flowInfo,
          currentStep: 1,
        }),
      }),
      on: {
        NEXT: [
          {
            // If auto-buy is enabled and user is verified, skip product info
            target: 'userInformation',
            guard: ({ context }) => context.userInfo.isAutoBuy && context.userInfo.isVerified,
          },
          {
            // Default path
            target: 'productInfo',
          },
        ],
        UPDATE: {
          actions: ({ context, event }) => {
            Object.assign(context, event.data)
          },
        },
      },
    },
    productInfo: {
      entry: assign({
        flowInfo: (context) => ({
          ...context.flowInfo,
          currentStep: 2,
        }),
      }),
      on: {
        NEXT: 'userInformation',
        BACK: 'productSelection',
        UPDATE: {
          actions: ({ context, event }) => {
            Object.assign(context, event.data)
          },
        },
      },
    },
    userInformation: {
      entry: assign({
        flowInfo: (context) => ({
          ...context.flowInfo,
          currentStep: 3,
        }),
      }),
      on: {
        NEXT: [
          {
            // If user is not verified, go to verification step
            guard: ({ context }) => !context.userInfo.isVerified,
            target: 'verification',
          },
          {
            // If user has a subscription and balance is sufficient, go to color selection
            guard: ({ context }) =>
              context.userInfo.subscription !== 'None' &&
              context.userInfo.balance >= context.product.price,
            target: 'colorSelection',
          },
          {
            // If user has insufficient balance, go to payment method selection
            guard: ({ context }) => context.userInfo.balance < context.product.price,
            target: 'paymentMethodSelection',
          },
          {
            // If user has a promo code, go to promo code step
            guard: ({ context }) => context.userInfo.hasPromoCode,
            target: 'promoCodeEntry',
          },
          {
            // Otherwise, go to confirmation
            target: 'confirmation',
          },
        ],
        BACK: 'productInfo',
        UPDATE: {
          actions: ({ context, event }) => {
            Object.assign(context, event.data)
          },
        },
      },
    },
    verification: {
      entry: assign({
        flowInfo: (context) => ({
          ...context.flowInfo,
          currentStep: 4,
        }),
      }),
      on: {
        NEXT: [
          {
            // If user has a subscription and KYC level is high enough, go to color selection
            guard: ({ context }) =>
              context.userInfo.subscription !== 'None' && context.userInfo.kycLevel === 'Level 3',
            target: 'colorSelection',
          },
          {
            // If user has insufficient balance, go to payment method selection
            guard: ({ context }) => context.userInfo.balance < context.product.price,
            target: 'paymentMethodSelection',
          },
          {
            // If user has a promo code, go to promo code step
            guard: ({ context }) => context.userInfo.hasPromoCode,
            target: 'promoCodeEntry',
          },
          {
            // Otherwise, go to confirmation
            target: 'confirmation',
          },
        ],
        BACK: 'userInformation',
        UPDATE: {
          actions: ({ context, event }) => {
            Object.assign(context, event.data)
          },
        },
        VERIFY: {
          actions: assign({
            userInfo: (context) => ({
              ...context.userInfo,
              isVerified: true,
            }),
          }),
        },
      },
    },
    colorSelection: {
      entry: assign({
        flowInfo: (context) => ({
          ...context.flowInfo,
          currentStep: 5,
        }),
      }),
      on: {
        NEXT: [
          {
            // If user has a promo code, go to promo code entry
            guard: ({ context }) => context.userInfo.hasPromoCode,
            target: 'promoCodeEntry',
          },
          {
            // If user has insufficient balance, go to payment method selection
            guard: ({ context }) => context.userInfo.balance < context.product.price,
            target: 'paymentMethodSelection',
          },
          {
            // Otherwise, go to confirmation
            target: 'confirmation',
          },
        ],
        BACK: [
          {
            target: 'userInformation',
            guard: ({ context }) => context.userInfo.isVerified,
          },
          {
            target: 'verification',
          },
        ],
        UPDATE: {
          actions: ({ context, event }) => {
            Object.assign(context, event.data)
          },
        },
        SELECT_COLOR: {
          actions: assign({
            userInfo: (context, event) => ({
              ...context.userInfo,
              selectedColor: event.color,
            }),
          }),
        },
      },
    },

    paymentMethodSelection: {
      entry: assign({
        flowInfo: (context) => ({
          ...context.flowInfo,
          currentStep: 6,
        }),
      }),
      on: {
        NEXT: [
          {
            // If user has a promo code, go to promo code entry
            guard: ({ context }) => context.userInfo.hasPromoCode,
            target: 'promoCodeEntry',
          },
          {
            // Otherwise, go to confirmation
            target: 'confirmation',
          },
        ],
        BACK: [
          {
            target: 'colorSelection',
            guard: ({ context }) => context.userInfo.subscription !== 'None',
          },
          {
            target: 'verification',
            guard: ({ context }) => !context.userInfo.isVerified,
          },
          {
            target: 'userInformation',
          },
        ],
        SELECT_PAYMENT: {
          actions: assign({
            userInfo: (context, event) => ({
              ...context.userInfo,
              selectedPaymentMethod: event.method,
            }),
          }),
        },
        UPDATE: {
          actions: ({ context, event }) => {
            Object.assign(context, event.data)
          },
        },
      },
    },

    promoCodeEntry: {
      entry: assign({
        flowInfo: (context) => ({
          ...context.flowInfo,
          currentStep: 7,
        }),
      }),
      on: {
        NEXT: 'confirmation',
        BACK: [
          {
            target: 'paymentMethodSelection',
            guard: ({ context }) => context.userInfo.balance < context.product.price,
          },
          {
            target: 'colorSelection',
            guard: ({ context }) => context.userInfo.subscription !== 'None',
          },
          {
            target: 'verification',
            guard: ({ context }) => !context.userInfo.isVerified,
          },
          {
            target: 'userInformation',
          },
        ],
        APPLY_PROMO: {
          actions: assign({
            userInfo: (context, event) => ({
              ...context.userInfo,
              promoCode: event.code,
            }),
            product: (context) => ({
              ...context.product,
              price: context.product.price * 0.9, // 10% discount
            }),
          }),
        },
        UPDATE: {
          actions: ({ context, event }) => {
            Object.assign(context, event.data)
          },
        },
      },
    },
    confirmation: {
      entry: assign({
        flowInfo: (context) => ({
          ...context.flowInfo,
          currentStep: 8,
        }),
      }),
      on: {
        BACK: [
          {
            target: 'promoCodeEntry',
            guard: ({ context }) => context.userInfo.hasPromoCode,
          },
          {
            target: 'paymentMethodSelection',
            guard: ({ context }) => context.userInfo.balance < context.product.price,
          },
          {
            target: 'colorSelection',
            guard: ({ context }) => context.userInfo.subscription !== 'None',
          },
          {
            target: 'verification',
            guard: ({ context }) => !context.userInfo.isVerified,
          },
          {
            target: 'userInformation',
          },
        ],
        SUBMIT: [
          {
            // If everything is valid, proceed to success
            target: 'processing',
          },
        ],
        UPDATE: {
          actions: ({ context, event }) => {
            Object.assign(context, event.data)
          },
        },
      },
    },

    processing: {
      tags: ['loading'],
      invoke: {
        id: 'processOrder',
        src: () => {
          // Simulate API call to process order
          return new Promise((resolve) => {
            setTimeout(() => {
              resolve({ orderId: 'ORD-' + Math.floor(Math.random() * 10000) })
            }, 1500)
          })
        },
        onDone: {
          target: 'success',
          actions: assign({
            orderResult: (_, event) => event.data,
          }),
        },
        onError: {
          target: 'confirmation',
          actions: assign({
            flowInfo: (context, event) => ({
              ...context.flowInfo,
              error: 'Failed to process order. Please try again.',
            }),
          }),
        },
      },
    },
    success: {
      type: 'final',
      entry: assign({
        flowInfo: (context) => ({
          ...context.flowInfo,
          currentStep: 10,
        }),
      }),
    },
  },
})

// Use the state machine in the component
const { snapshot: state, send } = useMachine(purchaseFlowMachine, {
  inspect,
})

// Helper functions to navigate between steps
const nextStep = () => {
  send({ type: 'NEXT' })
}

const prevStep = () => {
  send({ type: 'BACK' })
}

// Helper functions for user interactions
const updateSelectedColor = (color: string) => {
  send({
    type: 'SELECT_COLOR',
    color,
  })
}

const selectPaymentMethod = (method: string) => {
  send({
    type: 'SELECT_PAYMENT',
    method,
  })
}

const applyPromoCode = (code: string) => {
  send({
    type: 'APPLY_PROMO',
    code,
  })
}

const verifyUser = () => {
  send('VERIFY')
}

const retryInitialization = () => {
  send('RETRY')
}

// Helper function to get current step number for progress bar
const getCurrentStep = () => {
  // For loading states, use the stored step in context
  if (state.value.hasTag('loading')) {
    return state.value.context.flowInfo.currentStep || 0
  }

  // For other states, determine step based on the current state
  switch (state.value.value) {
    case 'initialization':
      return 0
    case 'productSelection':
      return 1
    case 'productInfo':
      return 2
    case 'userInformation':
      return 3
    case 'verification':
      return 4
    case 'colorSelection':
      return 5
    case 'paymentMethodSelection':
      return 6
    case 'promoCodeEntry':
      return 7
    case 'confirmation':
      return 8
    case 'processing':
      return 9
    case 'success':
      return 10
    case 'error':
      return 0
    default:
      return 0
  }
}

// Calculate total steps based on user conditions
const getTotalSteps = () => {
  let steps = 4 // Base steps: productSelection, productInfo, userInformation, success

  // Add conditional steps
  if (!state.value.context.userInfo.isVerified) {
    steps++ // Add verification step
  }

  if (state.value.context.userInfo.subscription !== 'None') {
    steps++ // Add color selection step
  }

  if (state.value.context.userInfo.balance < state.value.context.product.price) {
    steps++ // Add payment method selection step
  }

  if (state.value.context.userInfo.hasPromoCode) {
    steps++ // Add promo code entry step
  }

  steps += 2 // Add confirmation and processing steps

  return steps
}

// Total number of steps
const totalSteps = getTotalSteps()
</script>

<template>
  <div class="bg-white p-6 rounded-lg shadow-md">
    <div class="mb-4">
      <div class="flex items-center justify-between mb-2">
        <h2 class="text-xl font-semibold">Complex Purchase Flow</h2>
        <div class="text-sm text-gray-500">Step {{ getCurrentStep() }} of {{ totalSteps }}</div>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-2.5">
        <div
          class="bg-blue-600 h-2.5 rounded-full"
          :style="{ width: `${(getCurrentStep() / totalSteps) * 100}%` }"
        ></div>
      </div>
    </div>

    <div class="min-h-[200px] mb-4">
      <!-- Initialization/Loading Step -->
      <div v-if="state.matches('initialization')" class="space-y-4">
        <h3 class="text-lg font-medium">Initializing Purchase Flow</h3>
        <p class="text-gray-600">Loading your personalized purchase experience...</p>
        <div class="flex justify-center items-center py-8">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>

      <!-- Error State -->
      <div v-if="state.matches('error')" class="space-y-4">
        <h3 class="text-lg font-medium text-red-600">Error</h3>
        <p class="text-gray-600">There was a problem initializing the purchase flow.</p>
        <div class="p-4 border rounded-md bg-red-50 text-red-700">
          <p>{{ state.context.flowInfo.error || 'Unknown error occurred' }}</p>
          <button
            @click="retryInitialization"
            class="mt-4 px-4 py-2 bg-red-600 text-white rounded-md"
          >
            Retry
          </button>
        </div>
      </div>

      <!-- Product Selection Step -->
      <div v-if="state.matches('productSelection')" class="space-y-4">
        <h3 class="text-lg font-medium">Product Selection</h3>
        <p class="text-gray-600">
          This is where the user would select products to purchase. This is now connected to XState
          for managing the flow.
        </p>
        <div class="p-4 border rounded-md bg-gray-50">
          <p class="font-medium">{{ state.context.product.name }}</p>
          <p class="text-gray-500">${{ state.context.product.price }}</p>
          <p class="text-xs text-gray-400 mt-1">Category: {{ state.context.product.category }}</p>

          <!-- Show auto-buy badge if enabled -->
          <div v-if="state.context.userInfo.isAutoBuy" class="mt-2">
            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
              Auto-Buy Enabled
            </span>
          </div>

          <!-- Show balance warning if applicable -->
          <div
            v-if="state.context.userInfo.balance < state.context.product.price"
            class="mt-2 p-2 bg-yellow-50 text-yellow-700 text-sm rounded"
          >
            Warning: Your balance (${{ state.context.userInfo.balance.toFixed(2) }}) is insufficient
            for this purchase.
          </div>
        </div>
      </div>

      <!-- Product Info Step -->
      <div v-if="state.matches('productInfo')" class="space-y-4">
        <h3 class="text-lg font-medium">Product Information</h3>
        <p class="text-gray-600">Detailed information about the selected product.</p>
        <div class="p-4 border rounded-md bg-gray-50">
          <p class="font-medium">{{ state.context.product.name }}</p>
          <p class="text-gray-600 mt-2">{{ state.context.product.description }}</p>
          <p class="text-gray-500 mt-2">${{ state.context.product.price }}</p>
          <div class="mt-2">
            <p class="text-sm text-gray-600">Available Colors:</p>
            <div class="flex space-x-2 mt-1">
              <span
                v-for="color in state.context.product.availableColors"
                :key="color"
                class="px-2 py-1 bg-gray-100 text-xs rounded"
              >
                {{ color }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- User Information Step -->
      <div v-if="state.matches('userInformation')" class="space-y-4">
        <h3 class="text-lg font-medium">User Information</h3>
        <p class="text-gray-600">
          This is where the user would enter their information. The flow might change based on user
          subscription tier, KYC level, etc.
        </p>
        <div class="space-y-2">
          <div class="flex items-center">
            <span class="w-32 text-gray-600">Subscription:</span>
            <span class="font-medium">{{ state.context.userInfo.subscription }}</span>
          </div>
          <div class="flex items-center">
            <span class="w-32 text-gray-600">KYC Level:</span>
            <span class="font-medium">{{ state.context.userInfo.kycLevel }}</span>
          </div>
          <div class="flex items-center">
            <span class="w-32 text-gray-600">Balance:</span>
            <span class="font-medium">${{ state.context.userInfo.balance.toFixed(2) }}</span>
          </div>
          <div class="flex items-center">
            <span class="w-32 text-gray-600">Verified:</span>
            <span class="font-medium">{{ state.context.userInfo.isVerified ? 'Yes' : 'No' }}</span>
          </div>
        </div>
      </div>

      <!-- Verification Step -->
      <div v-if="state.matches('verification')" class="space-y-4">
        <h3 class="text-lg font-medium">Verification Required</h3>
        <p class="text-gray-600">
          Your account needs to be verified before proceeding with the purchase.
        </p>
        <div class="p-4 border rounded-md bg-yellow-50 text-yellow-700">
          <p class="font-medium">Verification Process</p>
          <p class="mt-2">Please complete the following steps to verify your account:</p>
          <ul class="list-disc ml-5 mt-2">
            <li>Upload a valid ID document</li>
            <li>Confirm your email address</li>
            <li>Complete the security questionnaire</li>
          </ul>
          <div class="mt-4">
            <p class="text-sm mb-2">
              Current KYC Level:
              <span class="font-medium">{{ state.context.userInfo.kycLevel }}</span>
            </p>
            <p class="text-sm mb-2">Required KYC Level: <span class="font-medium">Level 3</span></p>

            <label class="flex items-center">
              <input type="checkbox" class="mr-2" @change="verifyUser" />
              <span>I have completed the verification process</span>
            </label>
          </div>
        </div>
      </div>

      <!-- Color Selection Step -->
      <div v-if="state.matches('colorSelection')" class="space-y-4">
        <h3 class="text-lg font-medium">Select Color</h3>
        <p class="text-gray-600">As a subscriber, you can choose from our available colors.</p>
        <div class="p-4 border rounded-md bg-gray-50">
          <p class="font-medium mb-2">Available Colors:</p>
          <div class="space-y-2">
            <div
              v-for="color in state.context.product.availableColors"
              :key="color"
              class="flex items-center"
            >
              <input
                type="radio"
                :id="color"
                :value="color"
                :checked="state.context.userInfo.selectedColor === color"
                @change="updateSelectedColor(color)"
                class="mr-2"
              />
              <label :for="color" class="cursor-pointer">{{ color }}</label>
            </div>
          </div>
          <p v-if="state.context.userInfo.selectedColor" class="mt-4 text-green-600">
            You selected: {{ state.context.userInfo.selectedColor }}
          </p>
        </div>
      </div>

      <!-- Payment Method Selection Step -->
      <div v-if="state.matches('paymentMethodSelection')" class="space-y-4">
        <h3 class="text-lg font-medium">Select Payment Method</h3>
        <p class="text-gray-600">
          Your balance is insufficient. Please select a payment method to complete your purchase.
        </p>
        <div class="p-4 border rounded-md bg-gray-50">
          <p class="font-medium mb-2">Available Payment Methods:</p>
          <div class="space-y-2">
            <div
              v-for="method in state.context.flowInfo.paymentMethods"
              :key="method"
              class="flex items-center"
            >
              <input
                type="radio"
                :id="method"
                :value="method"
                :checked="state.context.userInfo.selectedPaymentMethod === method"
                @change="selectPaymentMethod(method)"
                class="mr-2"
              />
              <label :for="method" class="cursor-pointer">{{ method }}</label>
            </div>
          </div>
          <p v-if="state.context.userInfo.selectedPaymentMethod" class="mt-4 text-green-600">
            You selected: {{ state.context.userInfo.selectedPaymentMethod }}
          </p>
        </div>
      </div>

      <!-- Promo Code Entry Step -->
      <div v-if="state.matches('promoCodeEntry')" class="space-y-4">
        <h3 class="text-lg font-medium">Enter Promo Code</h3>
        <p class="text-gray-600">You're eligible to use a promo code for this purchase.</p>
        <div class="p-4 border rounded-md bg-gray-50">
          <div class="flex items-center">
            <input
              type="text"
              placeholder="Enter promo code"
              class="flex-1 p-2 border rounded-md mr-2"
              v-model="state.context.userInfo.promoCode"
            />
            <button
              @click="applyPromoCode(state.context.userInfo.promoCode)"
              class="px-4 py-2 bg-blue-600 text-white rounded-md"
              :disabled="!state.context.userInfo.promoCode"
            >
              Apply
            </button>
          </div>
          <p v-if="state.context.userInfo.promoCode" class="mt-4 text-green-600">
            Promo code "{{ state.context.userInfo.promoCode }}" applied! You'll get 10% off.
          </p>
        </div>
      </div>

      <!-- Processing Step -->
      <div v-if="state.matches('processing')" class="space-y-4">
        <h3 class="text-lg font-medium">Processing Your Order</h3>
        <p class="text-gray-600">Please wait while we process your purchase...</p>
        <div class="flex justify-center items-center py-8">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
        </div>
      </div>

      <!-- Confirmation Step -->
      <div v-if="state.matches('confirmation')" class="space-y-4">
        <h3 class="text-lg font-medium">Confirmation</h3>
        <p class="text-gray-600">
          This is the final step where the user would confirm their purchase. The purchase data
          would be sent to the backend.
        </p>
        <div class="p-4 border rounded-md bg-green-50 text-green-700">
          <p class="font-medium">Order Summary</p>
          <div class="mt-2 space-y-2">
            <div class="flex justify-between">
              <span>Product:</span>
              <span>{{ state.context.product.name }}</span>
            </div>
            <div class="flex justify-between">
              <span>Category:</span>
              <span>{{ state.context.product.category }}</span>
            </div>
            <div class="flex justify-between">
              <span>Price:</span>
              <span>${{ state.context.product.price.toFixed(2) }}</span>
            </div>
            <div v-if="state.context.userInfo.selectedColor" class="flex justify-between">
              <span>Color:</span>
              <span>{{ state.context.userInfo.selectedColor }}</span>
            </div>
            <div v-if="state.context.userInfo.selectedPaymentMethod" class="flex justify-between">
              <span>Payment Method:</span>
              <span>{{ state.context.userInfo.selectedPaymentMethod }}</span>
            </div>
            <div v-if="state.context.userInfo.promoCode" class="flex justify-between">
              <span>Promo Code:</span>
              <span>{{ state.context.userInfo.promoCode }}</span>
            </div>
            <div class="border-t pt-2 mt-2 flex justify-between font-medium">
              <span>Total:</span>
              <span>${{ state.context.product.price.toFixed(2) }}</span>
            </div>
          </div>

          <!-- Show error if there was a processing error -->
          <div v-if="state.context.flowInfo.error" class="mt-4 p-2 bg-red-50 text-red-700 rounded">
            {{ state.context.flowInfo.error }}
          </div>
        </div>
      </div>

      <!-- Success Step -->
      <div v-if="state.matches('success')" class="space-y-4">
        <h3 class="text-lg font-medium">Success!</h3>
        <p class="text-gray-600">Your purchase has been successfully completed.</p>
        <div class="p-4 border rounded-md bg-green-50 text-green-700">
          <p class="font-medium">Thank you for your purchase!</p>
          <p class="mt-2">Order details:</p>
          <div class="mt-2 space-y-1">
            <div class="flex justify-between">
              <span>Order ID:</span>
              <span>{{ state.context.orderResult?.orderId || 'N/A' }}</span>
            </div>
            <div class="flex justify-between">
              <span>Product:</span>
              <span>{{ state.context.product.name }}</span>
            </div>
            <div class="flex justify-between">
              <span>Category:</span>
              <span>{{ state.context.product.category }}</span>
            </div>
            <div v-if="state.context.userInfo.selectedColor" class="flex justify-between">
              <span>Color:</span>
              <span>{{ state.context.userInfo.selectedColor }}</span>
            </div>
            <div v-if="state.context.userInfo.selectedPaymentMethod" class="flex justify-between">
              <span>Payment Method:</span>
              <span>{{ state.context.userInfo.selectedPaymentMethod }}</span>
            </div>
            <div v-if="state.context.userInfo.promoCode" class="flex justify-between">
              <span>Promo Code:</span>
              <span>{{ state.context.userInfo.promoCode }} (10% discount applied)</span>
            </div>
            <div class="flex justify-between">
              <span>Total:</span>
              <span>${{ state.context.product.price.toFixed(2) }}</span>
            </div>
          </div>

          <div class="mt-4 p-2 bg-blue-50 text-blue-700 rounded text-sm">
            <p class="font-medium">Purchase Flow Summary:</p>
            <ul class="list-disc ml-5 mt-1">
              <li>Auto-Buy: {{ state.context.userInfo.isAutoBuy ? 'Enabled' : 'Disabled' }}</li>
              <li>
                Verification Required: {{ !state.context.userInfo.isVerified ? 'Yes' : 'No' }}
              </li>
              <li>Subscription: {{ state.context.userInfo.subscription }}</li>
              <li>KYC Level: {{ state.context.userInfo.kycLevel }}</li>
              <li>
                Payment Method: {{ state.context.userInfo.selectedPaymentMethod || 'Balance' }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <div class="flex justify-between">
      <button
        @click="prevStep"
        class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md disabled:opacity-50"
        :disabled="
          state.matches('productSelection') ||
          state.matches('success') ||
          state.matches('initialization') ||
          state.matches('processing')
        "
      >
        Back
      </button>

      <div>
        <!-- Show verification status if in verification step -->
        <div
          v-if="state.matches('verification') && state.context.userInfo.isVerified"
          class="text-green-600 text-sm mb-2"
        >
          Verification completed! You can now proceed.
        </div>

        <!-- Show color selection requirement if in color selection step -->
        <div
          v-if="state.matches('colorSelection') && !state.context.userInfo.selectedColor"
          class="text-yellow-600 text-sm mb-2"
        >
          Please select a color to continue.
        </div>

        <!-- Show payment method requirement if in payment method selection step -->
        <div
          v-if="
            state.matches('paymentMethodSelection') && !state.context.userInfo.selectedPaymentMethod
          "
          class="text-yellow-600 text-sm mb-2"
        >
          Please select a payment method to continue.
        </div>

        <!-- Next button for most steps -->
        <button
          v-if="
            !state.matches('confirmation') &&
            !state.matches('success') &&
            !state.matches('initialization') &&
            !state.matches('processing') &&
            !state.matches('error')
          "
          @click="nextStep"
          class="px-4 py-2 bg-blue-600 text-white rounded-md disabled:opacity-50"
          :disabled="
            (state.matches('colorSelection') && !state.context.userInfo.selectedColor) ||
            (state.matches('paymentMethodSelection') &&
              !state.context.userInfo.selectedPaymentMethod)
          "
        >
          Next
        </button>

        <!-- Confirm button for confirmation step -->
        <button
          v-if="state.matches('confirmation')"
          @click="send({ type: 'SUBMIT' })"
          class="px-4 py-2 bg-green-600 text-white rounded-md"
        >
          Confirm Purchase
        </button>
      </div>
    </div>

    <!-- Debug information -->
    <div class="mt-6 p-4 border rounded bg-gray-50 text-xs">
      <div class="flex items-center justify-between mb-2">
        <h4 class="font-medium">Debug Information</h4>
        <button
          @click="send('RETRY')"
          class="px-2 py-1 bg-blue-600 text-white rounded-md text-xs"
          title="Reset and restart the flow"
        >
          Reset Flow
        </button>
      </div>
      <div class="grid grid-cols-2 gap-4">
        <div>
          <h5 class="font-medium">Current State:</h5>
          <pre class="mt-1 bg-gray-100 p-2 rounded overflow-auto max-h-20">{{ state.value }}</pre>
        </div>
        <div>
          <h5 class="font-medium">User Info:</h5>
          <div class="mt-1 bg-gray-100 p-2 rounded overflow-auto max-h-20">
            <div>
              <span class="font-medium">Subscription:</span>
              {{ state.context.userInfo.subscription }}
            </div>
            <div>
              <span class="font-medium">Verified:</span>
              {{ state.context.userInfo.isVerified ? 'Yes' : 'No' }}
            </div>
            <div>
              <span class="font-medium">KYC Level:</span> {{ state.context.userInfo.kycLevel }}
            </div>
            <div>
              <span class="font-medium">Balance:</span> ${{
                state.context.userInfo.balance.toFixed(2)
              }}
            </div>
            <div>
              <span class="font-medium">Auto-Buy:</span>
              {{ state.context.userInfo.isAutoBuy ? 'Enabled' : 'Disabled' }}
            </div>
            <div>
              <span class="font-medium">Has Promo:</span>
              {{ state.context.userInfo.hasPromoCode ? 'Yes' : 'No' }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
