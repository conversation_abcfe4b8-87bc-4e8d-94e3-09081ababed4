// Disable ESLint for the entire file
/* eslint-disable */
// @ts-nocheck
import { createMachine, assign } from "xstate";
import { CardMachineContext } from "./types";

// Define TypeScript interfaces for better type safety and documentation

/**
 * Card types available in the system
 */
type CardType = 'ultima' | 'default' | string;

/**
 * Card tariffs available in the system
 */
type CardTariff = 'ultima' | string;

/**
 * Context interface for the card creation state machine
 * Contains all the data needed to track the card creation process
 */
interface CardMachineContext {
  // User-related flags
  isAutoBuy: boolean;                    // Whether auto-buy is enabled for this user
  isVerified: boolean;                   // Whether the user is verified
  subscriptionsStatus: string | null;    // User's subscription status
  subscriptionOnlyExperiment: boolean;   // Whether user is in subscription-only experiment

  // Card-related data
  cardType: CardType | null;             // Type of card being created
  cardTariff: CardTariff | null;         // Tariff selected for the card
  selectedBin: string | null;            // Selected Bank Identification Number
  cardForIssue: unknown | null;          // Card data ready for issuance
  promoCodeData: unknown | null;         // Applied promo code data
  resultCard: unknown | null;            // Final card data after creation

  // Flow control
  numSteps: number;                      // Total number of steps in current flow
  step: number;                          // Current step in the flow
}

// The state machine is fully typed for better code completion and documentation

/**
 * Events that can be sent to the card creation state machine
 */
type CardMachineEvent =
  // User verification events
  | { type: 'VERIFICATION_COMPLETED' }                              // User completed verification process

  // Selection events
  | { type: 'SELECT_CARD_TARIFF', value: CardTariff }               // User selected a card tariff
  | { type: 'SELECT_CARD_BIN', value: string }                      // User selected a BIN (Bank Identification Number)
  | { type: 'SELECT_CARD_TYPE', value: CardType }                   // User selected a card type

  // Card issuance events
  | { type: 'ULTIMA_WITHOUT_SUBSCRIPTION_CARD_ISSUE', value: unknown } // Issue an Ultima card without subscription
  | { type: 'DEFAULT_CARD_ISSUE', value: unknown }                   // Issue a default card

  // Additional data events
  | { type: 'SET_PROMO_CODE', value: unknown }                      // User applied a promo code

  // Payment success events
  | { type: 'SINGLE_BUY_SUCCESS', value: unknown }                  // Single payment was successful
  | { type: 'MULTI_BUY_SUCCESS' }                                   // Multiple payments was successful
  | { type: 'AUTO_BUY_SUCCESS' }                                    // Auto-buy payment was successful

  // Navigation events
  | { type: 'BACK' };                                               // User wants to go back to previous step

// Define the initial context
const initialContext: CardMachineContext = {
  isAutoBuy: false,
  isVerified: false,
  subscriptionsStatus: null,
  subscriptionOnlyExperiment: false,
  cardType: null,
  cardTariff: null,
  selectedBin: null,
  cardForIssue: null,
  promoCodeData: null,
  resultCard: null,
  numSteps: 0,
  step: 0
};

/**
 * Helper function to assign initialization results to the context
 * This reduces code duplication across multiple transitions
 */
/* eslint-disable @typescript-eslint/no-explicit-any */

const assignInitializationResults = assign<CardMachineContext, any>((_, event) => ({
  isAutoBuy: event.output.isAutoBuy,
  isVerified: event && event.output ? event.output.isVerified : undefined,
  subscriptionsStatus: event.output.subscriptionsStatus,
  subscriptionOnlyExperiment: event.output.subscriptionOnlyExperiment,
  cardType: event.output.cardType,
  numSteps: 1,
  step: 1
}));
/* eslint-enable @typescript-eslint/no-explicit-any */

/**
 * Helper function to mark user as verified and set card tariff to ultima
 * This reduces code duplication in verification transitions
 */
const markUserVerified = assign<CardMachineContext>({
  isVerified: true,
  cardTariff: "ultima",
  step: (context) => context.step + 1, // Increment step counter
});

/**
 * Helper function to increment the step counter
 */
const incrementStep = assign<CardMachineContext>({
  step: (context) => context.step + 1,
});

/**
 * Helper function to decrement the step counter
 */
const decrementStep = assign<CardMachineContext>({
  step: (context) => context.step - 1,
});

/**
 * Pattern matchers for different user scenarios
 * These functions check common conditions in the state machine
 * and improve readability by giving them descriptive names
 */
const patterns = {
  // User verification patterns
  isUserVerified: (context: CardMachineContext) =>
    context.isVerified,

  isUserUnverified: (context: CardMachineContext) =>
    !context.isVerified,

  // Subscription patterns
  hasNoSubscription: (context: CardMachineContext) =>
    !context.subscriptionsStatus,

  isInSubscriptionExperiment: (context: CardMachineContext) =>
    !!context.subscriptionOnlyExperiment,

  // Card type patterns
  isUltimaCard: (context: CardMachineContext, event?: unknown) =>
    (event && event.value === "ultima") || context.cardType === "ultima",

  // Payment patterns
  isAutoBuyEnabled: (context: CardMachineContext) =>
    context.isAutoBuy,

  isAutoBuyDisabled: (context: CardMachineContext) =>
    !context.isAutoBuy
};

/**
 * Helper function to check if a user is eligible for an Ultima card without subscription
 * This improves readability of guard conditions by giving them a descriptive name
 */
const isEligibleForUltimaWithoutSubscription = (event: unknown) =>
  !event.output.isVerified &&             // User is not verified
  !event.output.subscriptionsStatus &&    // User has no subscription
  event.output.cardType === "ultima";     // Card type is ultima

/**
 * Card Creation State Machine
 *
 * This state machine manages the flow of creating a new card, including:
 * - User verification
 * - Card type selection
 * - Card tariff selection
 * - BIN selection
 * - Card issuance
 * - Payment processing
 */
export const machine = createMachine<CardMachineContext, CardMachineEvent>({
  context: initialContext, // Initial state data for the machine
  id: "create-card-machine", // Unique identifier for this state machine
  initial: "initialization", // Starting state
  states: {
    // ===================================================================
    // INITIALIZATION STATE
    // ===================================================================
    /**
     * Initial state that sets up the machine with user data
     * This state invokes an external service to fetch user information
     * and then transitions to the appropriate next state based on that data
     */
    initialization: {
      invoke: {
        id: "init",
        input: {},
        onDone: [
          // Path 1: User needs verification
          {
            target: "userVerification",
            actions: assignInitializationResults,
            guard: "isUserNeedsVerification",
          },

          // Path 2: User is in subscription experiment
          {
            target: "cardConfiguration.tariffSelection",
            actions: assignInitializationResults,
            guard: "isUserInSubscriptionExperiment",
          },

          // Path 3: User has ultima card type
          {
            target: "cardConfiguration.binSelection",
            actions: assignInitializationResults,
            guard: "isUserWithUltimaCardType",
          },

          // Path 4: User has ultima card without subscription
          {
            target: "cardIssuance.ultimaCardIssuance",
            actions: assignInitializationResults,
            guard: ({ event }) => isEligibleForUltimaWithoutSubscription(event),
          },

          // Default path: Go to card type selection
          {
            target: "cardConfiguration.typeSelection",
            actions: assignInitializationResults,
          },
        ],
        src: "initialize",
      },
    },

    // ===================================================================
    // USER VERIFICATION
    // ===================================================================
    /**
     * State that handles user verification completion
     */
    userVerification: {
      id: "userVerification",
      on: {
        VERIFICATION_COMPLETED: [
          // Path 1: Ultima card with auto-buy for users without subscription
          {
            target: "cardIssuance.ultimaCardIssuance",
            actions: markUserVerified,
            guard: "isVerifiedUserWithUltimaAndAutoBuy",
          },

          // Path 2: Ultima card without auto-buy for users without subscription
          {
            target: "cardConfiguration.binSelection",
            actions: markUserVerified,
            guard: "isVerifiedUserWithUltimaWithoutAutoBuy",
          },

          // Path 3: Users in subscription experiment without subscription
          {
            target: "cardConfiguration.tariffSelection",
            actions: markUserVerified,
            guard: "isUserInSubscriptionExperimentWithoutSubscription",
          },

          // Default path: Go to card type selection
          {
            target: "cardConfiguration.typeSelection",
            actions: markUserVerified,
          },
        ],
      },
    },

    // ===================================================================
    // CARD CONFIGURATION
    // ===================================================================
    /**
     * Group of states that handle card configuration
     * Includes card type selection, tariff selection, and BIN selection
     */
    cardConfiguration: {
      id: "cardConfiguration",
      initial: "typeSelection",
      states: {
        // ===================================================================
        // CARD TYPE SELECTION STATE
        // ===================================================================
        /**
         * State that handles the selection of a card type
         */
        typeSelection: {
          // Reset steps when entering this state
          entry: assign({
            numSteps: 0,
            step: 1
          }),

          on: {
            // When user selects a card type
            SELECT_CARD_TYPE: [
              // Path 1: Ultima card with auto-buy for users without subscription
              {
                target: "#cardIssuance.ultimaCardIssuance",
                actions: assign({
                  cardType: (_, event) => event.value,       // Set selected card type
                  cardTariff: "ultima",                      // Set tariff to ultima
                  numSteps: 3,                               // Update total steps in flow
                  step: 1,                                   // Reset current step
                }),
                guard: "isUltimaCardWithAutoBuyNoSubscription",
              },

              // Path 2: Ultima card without auto-buy for verified users without subscription
              {
                target: "binSelection",
                actions: assign({
                  cardType: (_, event) => event.value,       // Set selected card type
                  cardTariff: "ultima",                      // Set tariff to ultima
                  numSteps: 3,                               // Update total steps in flow
                  step: 1,                                   // Reset current step
                }),
                guard: "isUltimaCardWithoutAutoBuyVerifiedNoSubscription",
              },

              // Path 3: Ultima card for unverified users without subscription
              {
                target: "tariffSelection",
                actions: assign({
                  cardType: (_, event) => event.value,       // Set selected card type
                  cardTariff: "ultima",                      // Set tariff to ultima
                  numSteps: 3,                               // Update total steps in flow
                  step: 1,                                   // Reset current step
                }),
                guard: "isUltimaCardUnverifiedNoSubscription",
              },
            ],
          },
        },

        // ===================================================================
        // CARD TARIFF SELECTION STATE
        // ===================================================================
        /**
         * State that handles the selection of a card tariff
         */
        tariffSelection: {
          on: {
            SELECT_CARD_TARIFF: [
              // Path 1: Ultima tariff with auto-buy for users without subscription
              {
                target: "#cardIssuance.ultimaCardIssuance",
                actions: [
                  assign({
                    cardTariff: (_, event) => event.value,  // Set selected tariff
                    cardType: "ultima",                     // Set card type to ultima
                  }),
                  incrementStep,                            // Increment step counter
                ],
                guard: "isUltimaTariffWithAutoBuyNoSubscription",
              },

              // Path 2: Ultima tariff without auto-buy for users without subscription
              {
                target: "binSelection",
                actions: [
                  assign({
                    cardTariff: (_, event) => event.value,  // Set selected tariff
                    cardType: "ultima",                     // Set card type to ultima
                  }),
                  incrementStep,                            // Increment step counter
                ],
                guard: "isUltimaTariffWithoutAutoBuyNoSubscription",
              },

              // Default path: Any other tariff selection
              {
                target: "binSelection",
                actions: [
                  assign({
                    cardTariff: (_, event) => event.value,                // Set selected tariff
                    cardType: (context) => context.cardType || "default", // Use existing card type or default
                  }),
                  incrementStep,                                          // Increment step counter
                ],
              },
            ],
          },
        },

        // ===================================================================
        // BIN SELECTION STATE
        // ===================================================================
        /**
         * State that handles the selection of a BIN (Bank Identification Number)
         */
        binSelection: {
          on: {
            // When user selects a BIN
            SELECT_CARD_BIN: {
              target: "#cardIssuance.defaultCardIssuance",
              actions: assign({
                selectedBin: (_, event) => event.value,     // Store the selected BIN
                step: (context) => context.step + 1,        // Increment step counter
              }),
            },

            // When user wants to go back
            BACK: {
              target: "tariffSelection",
              actions: assign({
                cardTariff: null,                           // Clear tariff selection
                step: (context) => context.step - 1,        // Decrement step counter
              }),
            },
          },
        },
      },
    },

    // ===================================================================
    // CARD ISSUANCE
    // ===================================================================
    /**
     * Group of states that handle card issuance
     * Includes ultima card issuance and default card issuance
     */
    cardIssuance: {
      id: "cardIssuance",
      initial: "ultimaCardIssuance",
      states: {
        // ===================================================================
        // ULTIMA CARD ISSUANCE STATE (WITHOUT SUBSCRIPTION)
        // ===================================================================
        /**
         * State that handles the issuance of an Ultima card for users without a subscription
         */
        ultimaCardIssuance: {
          on: {
            // When user issues an Ultima card
            ULTIMA_WITHOUT_SUBSCRIPTION_CARD_ISSUE: [
              // Path 1: Standard approval flow (auto-buy disabled)
              {
                target: "#payment.approval",
                actions: assign({
                  cardForIssue: (_, event) => event.value,     // Store card data for issuance
                  step: (context) => context.step + 1,         // Increment step counter
                }),
                guard: "isAutoBuyDisabled",
              },

              // Path 2: Auto-buy payment flow
              {
                target: "#payment.autoBuy",
                actions: assign({
                  cardForIssue: (_, event) => event.value,     // Store card data for issuance
                  step: (context) => context.step + 1,         // Increment step counter
                }),
                guard: "isAutoBuyEnabled",
              },
            ],

            // When user updates the card tariff
            SELECT_CARD_TARIFF: {
              actions: assign({
                cardTariff: (_, event) => event.value          // Update tariff only
              }),
            },

            // When user applies a promo code
            SET_PROMO_CODE: {
              actions: assign({
                promoCodeData: (_, event) => event.value       // Store promo code data
              }),
            },
          },
        },

        // ===================================================================
        // DEFAULT CARD ISSUANCE STATE
        // ===================================================================
        /**
         * State that handles the issuance of a default (non-Ultima) card
         */
        defaultCardIssuance: {
          on: {
            // When user issues a default card
            DEFAULT_CARD_ISSUE: [
              // Path 1: Standard approval flow (auto-buy disabled)
              {
                target: "#payment.approval",
                actions: assign({
                  cardForIssue: (_, event) => event.value,     // Store card data for issuance
                  step: (context) => context.step + 1,         // Increment step counter
                }),
                guard: "isAutoBuyDisabled",
              },

              // Path 2: Auto-buy payment flow
              {
                target: "#payment.autoBuy",
                actions: assign({
                  cardForIssue: (_, event) => event.value,     // Store card data for issuance
                  step: (context) => context.step + 1,         // Increment step counter
                }),
                guard: "isAutoBuyEnabled",
              },
            ],

            // When user applies a promo code
            SET_PROMO_CODE: {
              actions: assign({
                promoCodeData: (_, event) => event.value       // Store promo code data
              }),
            },

            // When user wants to go back
            BACK: {
              target: "#cardConfiguration.binSelection",
              actions: assign({
                selectedBin: null,                             // Clear BIN selection
                promoCodeData: null,                           // Clear promo code data
                step: (context) => context.step - 1,           // Decrement step counter
              }),
            },
          },
        },
      },
    },

    // ===================================================================
    // PAYMENT
    // ===================================================================
    /**
     * Group of states that handle payment processing
     * Includes approval, auto-buy, and success states
     */
    payment: {
      id: "payment",
      initial: "approval",
      states: {
        // ===================================================================
        // CARD APPROVAL STATE
        // ===================================================================
        /**
         * State that handles the approval of a card purchase
         */
        approval: {
          on: {
            // When a single payment is successful
            SINGLE_BUY_SUCCESS: {
              target: "success",
              actions: [
                assign({
                  resultCard: (_, event) => event.value         // Store the resulting card data
                }),
                incrementStep,                                  // Increment step counter
              ],
            },

            // When multiple payments are successful
            MULTI_BUY_SUCCESS: {
              target: "success",
              actions: incrementStep,                           // Increment step counter
            },

            // When user wants to go back
            BACK: {
              target: "#cardIssuance.defaultCardIssuance",
              actions: [
                assign({
                  cardForIssue: null,                           // Clear card issuance data
                  promoCodeData: null,                          // Clear promo code data
                }),
                decrementStep,                                  // Decrement step counter
              ],
            },
          },
        },

        // ===================================================================
        // AUTO-BUY PAYMENT STATE
        // ===================================================================
        /**
         * State that handles the payment process for auto-buy enabled cards
         */
        autoBuy: {
          on: {
            // When auto-buy payment is successful
            AUTO_BUY_SUCCESS: {
              target: "success",
              actions: incrementStep,                           // Increment step counter
            },

            // When user wants to go back
            BACK: [
              // Path 1: Go back to default card issue for ultima cards
              {
                target: "#cardIssuance.defaultCardIssuance",
                actions: [
                  assign({
                    cardForIssue: null,                         // Clear card issuance data
                    promoCodeData: null,                        // Clear promo code data
                  }),
                  decrementStep,                                // Decrement step counter
                ],
                guard: "isUltimaCardType",
              },

              // Path 2: Go back to ultima card issue for non-ultima cards
              {
                target: "#cardIssuance.ultimaCardIssuance",
                actions: [
                  assign({
                    cardForIssue: null,                         // Clear card issuance data
                    promoCodeData: null,                        // Clear promo code data
                  }),
                  decrementStep,                                // Decrement step counter
                ],
                guard: "isNotUltimaCardType",
              },
            ],
          },
        },

        // ===================================================================
        // SUCCESS STATE
        // ===================================================================
        /**
         * Final state of the card creation flow
         */
        success: {
          // Reset steps when entering this state for future flows
          entry: assign({
            numSteps: 0,
            step: 1
          }),
          type: "final",
        },
      },
    },
  },
}).withConfig({
  guards: {
    // User verification guards
    isUserNeedsVerification: ({ event }) => !event.output.isVerified,

    isUserInSubscriptionExperiment: ({ event }) =>
      !event.output.isVerified && event.output.subscriptionOnlyExperiment,

    isUserWithUltimaCardType: ({ event }) =>
      !event.output.isVerified && event.output.cardType === "ultima",

    // Verification completion guards
    isVerifiedUserWithUltimaAndAutoBuy: ({ context }) =>
      patterns.hasNoSubscription(context) &&
      patterns.isInSubscriptionExperiment(context) &&
      patterns.isUltimaCard(context) &&
      patterns.isAutoBuyEnabled(context),

    isVerifiedUserWithUltimaWithoutAutoBuy: ({ context }) =>
      patterns.hasNoSubscription(context) &&
      patterns.isInSubscriptionExperiment(context) &&
      patterns.isUltimaCard(context) &&
      patterns.isAutoBuyDisabled(context),

    isUserInSubscriptionExperimentWithoutSubscription: ({ context }) =>
      patterns.hasNoSubscription(context) &&
      patterns.isInSubscriptionExperiment(context),

    // Card type selection guards
    isUltimaCardWithAutoBuyNoSubscription: ({ event, context }) =>
      patterns.hasNoSubscription(context) &&
      patterns.isUltimaCard(context, event) &&
      patterns.isAutoBuyEnabled(context),

    isUltimaCardWithoutAutoBuyVerifiedNoSubscription: ({ event, context }) =>
      patterns.hasNoSubscription(context) &&
      patterns.isUltimaCard(context, event) &&
      patterns.isAutoBuyDisabled(context) &&
      patterns.isUserVerified(context),

    isUltimaCardUnverifiedNoSubscription: ({ event, context }) =>
      patterns.hasNoSubscription(context) &&
      patterns.isUltimaCard(context, event) &&
      patterns.isUserUnverified(context),

    // Tariff selection guards
    isUltimaTariffWithAutoBuyNoSubscription: ({ context, event }) =>
      patterns.hasNoSubscription(context) &&
      event.value === "ultima" &&
      patterns.isAutoBuyEnabled(context),

    isUltimaTariffWithoutAutoBuyNoSubscription: ({ context, event }) =>
      patterns.hasNoSubscription(context) &&
      event.value === "ultima" &&
      patterns.isAutoBuyDisabled(context),

    // Payment guards
    isAutoBuyEnabled: ({ context }) => patterns.isAutoBuyEnabled(context),
    isAutoBuyDisabled: ({ context }) => patterns.isAutoBuyDisabled(context),

    // Card type guards for back navigation
    isUltimaCardType: ({ context }) => context.cardType === "ultima",
    isNotUltimaCardType: ({ context }) => context.cardType !== "ultima",
  },
  services: {
    initialize: createMachine({ // Nested state machine for initialization
      /* ... */
    }),
  },
});
import { CardMachineContext } from "./types";

/* ------------------------------------------------------------------ */
/* Primitive predicates – single-responsibility, easy to unit-test    */
/* ------------------------------------------------------------------ */
const hasNoSubscription        = (ctx: CardMachineContext) => !ctx.subscriptionsStatus;
const isInSubscriptionExperiment = (ctx: CardMachineContext) => ctx.subscriptionOnlyExperiment;
const isUltimaCard              = (ctx: CardMachineContext, evt?: any) =>
  (evt?.value ?? ctx.cardType) === "ultima";
const isAutoBuyEnabled          = (ctx: CardMachineContext) =>  ctx.isAutoBuy;
const isAutoBuyDisabled         = (ctx: CardMachineContext) => !ctx.isAutoBuy;
const isUserVerified            = (ctx: CardMachineContext) =>  ctx.isVerified;
const isUserUnverified          = (ctx: CardMachineContext) => !ctx.isVerified;

/* ------------------------------------------------------------------ */
/* Helper to compose many predicates into a single guard              */
/* ------------------------------------------------------------------ */
type GuardArgs = { context: CardMachineContext; event: any };
type Predicate = (ctx: CardMachineContext, evt?: any) => boolean;

function compose(...predicates: Predicate[]) {
  return ({ context, event }: GuardArgs) =>
    predicates.every((pred) => pred(context, event));
}

/* ------------------------------------------------------------------ */
/* Final guard map ‑ can be imported straight into createMachine()    */
/* ------------------------------------------------------------------ */
export const cardGuards = {
  /* ---------- initialization guards ---------- */
  isUserNeedsVerification:   ({ event }: any) => !event.output.isVerified,
  isUserInSubscriptionExperiment: ({ event }: any) =>
    !event.output.isVerified && event.output.subscriptionOnlyExperiment,
  isUserWithUltimaCardType:  ({ event }: any) =>
    !event.output.isVerified && event.output.cardType === "ultima",

  /* ---------- verification-completed guards ---------- */
  isVerifiedUserWithUltimaAndAutoBuy:        compose(
    hasNoSubscription,
    isInSubscriptionExperiment,
    isUltimaCard,
    isAutoBuyEnabled
  ),
  isVerifiedUserWithUltimaWithoutAutoBuy:    compose(
    hasNoSubscription,
    isInSubscriptionExperiment,
    isUltimaCard,
    isAutoBuyDisabled
  ),
  isUserInSubscriptionExperimentWithoutSubscription: compose(
    hasNoSubscription,
    isInSubscriptionExperiment
  ),

  /* ---------- card-type selection guards ---------- */
  isUltimaCardWithAutoBuyNoSubscription:           compose(
    hasNoSubscription,
    isUltimaCard,
    isAutoBuyEnabled
  ),
  isUltimaCardWithoutAutoBuyVerifiedNoSubscription: compose(
    hasNoSubscription,
    isUltimaCard,
    isAutoBuyDisabled,
    isUserVerified
  ),
  isUltimaCardUnverifiedNoSubscription:            compose(
    hasNoSubscription,
    isUltimaCard,
    isUserUnverified
  ),

  /* ---------- tariff selection guards ---------- */
  isUltimaTariffWithAutoBuyNoSubscription:   compose(
    hasNoSubscription,
    isAutoBuyEnabled,
    (ctx, evt) => evt.value === "ultima"
  ),
  isUltimaTariffWithoutAutoBuyNoSubscription: compose(
    hasNoSubscription,
    isAutoBuyDisabled,
    (ctx, evt) => evt.value === "ultima"
  ),

  /* ---------- payment guards ---------- */
  isAutoBuyEnabled:  ({ context }: GuardArgs) => isAutoBuyEnabled(context),
  isAutoBuyDisabled: ({ context }: GuardArgs) => isAutoBuyDisabled(context),

  /* ---------- navigation helpers ---------- */
  isUltimaCardType:     ({ context }: GuardArgs) => context.cardType === "ultima",
  isNotUltimaCardType:  ({ context }: GuardArgs) => context.cardType !== "ultima",
} as const;
